<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏中心</title>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 复用样式 */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 30px;
        }

        /* 头部和导航 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 25px 0;
            color: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .nav {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .nav-links {
            display: flex;
            align-items: center;
            gap: 40px;
        }

        .nav-links a {
            color: #2c3e50;
            text-decoration: none;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 25px;
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        /* 英雄区域 */
        .hero {
            text-align: center;
            /* padding: 120px 0; */
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            margin-bottom: 80px;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .hero h2 {
            font-size: 3.5em;
            color: white;
            margin-bottom: 25px;
            font-weight: 700;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
        }

        .hero p {
            font-size: 1.4em;
            color: rgba(255,255,255,0.9);
            margin-bottom: 40px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            z-index: 1;
        }

        /* 登录注册按钮样式 */
        .nav-auth {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .nav-auth a {
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-auth .login-btn {
            color: #667eea;
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .nav-auth .login-btn:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        }

        .nav-auth .register-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .nav-auth .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        /* 发布按钮样式 */
        .publish-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
            color: white !important;
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .publish-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        }

        .logout-btn {
            color: #2c3e50 !important;
            background: rgba(44, 62, 80, 0.1);
            border: 1px solid rgba(44, 62, 80, 0.2);
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .logout-btn:hover {
            background: rgba(44, 62, 80, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(44, 62, 80, 0.2);
        }

        .user-btns {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-btns a {
            text-decoration: none;
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* 主要内容区域 */
        .main-content {
            background: white;
            border-radius: 30px 30px 0 0;
            margin-top: 0;
            position: relative;
            z-index: 10;
            box-shadow: 0 -10px 40px rgba(0,0,0,0.1);
            padding: 60px 0;
        }



        /* 游戏网格布局 */
        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 30px;
            padding: 60px 0;
        }

        /* 游戏卡片样式 */
        .game-card {
            background: white;
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(102, 126, 234, 0.1);
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }

        .game-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 50px rgba(102, 126, 234, 0.2);
        }

        .game-image {
            width: 120px;
            height: 120px;
            border-radius: 15px;
            overflow: hidden;
            flex-shrink: 0;
            position: relative;
        }

        .game-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .game-card:hover .game-image img {
            transform: scale(1.05);
        }

        .game-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 12px;
            height: 100%;
        }

        .game-title {
            font-size: 1.3em;
            font-weight: 700;
            color: #2c3e50;
            margin: 0;
            line-height: 1.3;
        }

        .game-meta {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 8px;
        }

        .game-category {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.85em;
            font-weight: 500;
        }

        .game-age {
            background: #ff9500;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.85em;
            font-weight: 500;
        }

        .game-description {
            color: #5a6c7d;
            font-size: 1em;
            line-height: 1.6;
            margin: 8px 0;
            flex: 1;
        }

        .game-actions {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-top: auto;
            padding-top: 15px;
        }

        .download-btn {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 10px 10px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 1em;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
        }

        .detail-btn {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            padding: 10px 16px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid rgba(102, 126, 234, 0.2);
            display: inline-block;
            font-size: 0.9em;
        }

        .detail-btn:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        /* 分类筛选 */
        .filter-section {
            background: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 40px;
            text-align: center;
        }

        .filter-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 10px 20px;
            border: 2px solid #667eea;
            background: transparent;
            color: #667eea;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .filter-btn.active,
        .filter-btn:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        /* 页脚样式 */
        .footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 40px 0 20px;
            margin-top: 0;
            position: relative;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            /*margin-bottom: 50px;*/
            position: relative;
            z-index: 1;
        }

        .footer-section h4 {
            font-size: 1.2em;
            margin-bottom: 15px;
            color: white;
            position: relative;
            padding-bottom: 8px;
            font-weight: 600;
        }

        .footer-section h4::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 30px;
            height: 2px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 1px;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 8px;
        }

        .footer-section ul li a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.95em;
            display: inline-block;
        }

        .footer-section ul li a:hover {
            color: #667eea;
            transform: translateX(5px);
        }

        .contact-info {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            color: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .contact-info:hover {
            color: white;
            transform: translateX(5px);
        }

        .contact-info i {
            margin-right: 10px;
            color: #667eea;
            font-size: 1em;
            width: 16px;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 15px;
            border-top: 1px solid rgba(255,255,255,0.1);
            color: rgba(255,255,255,0.6);
            position: relative;
            z-index: 1;
        }

        .footer-bottom p {
            margin-bottom: 5px;
            font-size: 0.85em;
            line-height: 1.4;
        }

        .footer-bottom a {
            color: rgba(255,255,255,0.8);
            transition: color 0.3s ease;
        }

        .footer-bottom a:hover {
            color: #667eea;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .games-grid {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 40px 0;
            }
            
            .game-card {
                flex-direction: column;
                text-align: center;
            }
            
            .game-image {
                width: 100%;
                height: 200px;
                align-self: center;
            }
            
            .hero h2 {
                font-size: 2.5em;
            }
            
            .filter-buttons {
                gap: 10px;
            }
            
            .filter-btn {
                padding: 8px 16px;
                font-size: 0.9em;
            }
        }
    </style>
    <link rel="stylesheet" href="css/all.min.css">
</head>
<body>
    <!-- 头部信息 -->
    <div class="header">
        <div class="container">
            <h1><img src="img/logo.png" height="50" /></h1>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav">
        <div class="container nav-container">
            <div class="nav-auth" id="navAuth">
                <!-- 登录前显示 -->
                <div class="auth-btns" id="authBtns">
                    <a href="register.html" class="register-btn">
                        <i class="fas fa-user-plus"></i>注册
                    </a>
                    <a href="login.html" class="login-btn">
                        <i class="fas fa-user"></i>登录
                    </a>
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>信息发布
                    </a>
                </div>
                <!-- 登录后显示 -->
                <div class="user-btns" id="userBtns" style="display: none;">
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>信息发布
                    </a>
                    <a href="javascript:void(0)" class="logout-btn" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>退出
                    </a>
                </div>
            </div>
            <div class="nav-links">
                <a href="index.html">首页</a>
                <a href="products-app.html" class="active">游戏中心</a>
                <!-- <a href="products-wx.html">小程序开发</a>
                <a href="products-soft.html">管理软件开发</a> -->
                <a href="articles.html">防沉迷公告</a>
                <a href="about.html">关于我们</a>
                <!-- <a href="contact.html">联系我们</a> -->
            </div>
        </div>
    </div>

    <!-- 游戏中心标题区域 -->
    <div class="hero">
        <!-- <div class="container">
            <h2>🎮 游戏中心</h2>
            <p>探索精彩游戏世界，发现你的下一个最爱</p>
        </div> -->
    </div>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">

            <!-- 游戏分类筛选 -->
            <!-- <section class="filter-section">
                <h3 style="margin-bottom: 20px; color: #2c3e50;">游戏分类</h3>
                <div class="filter-buttons">
                    <button class="filter-btn active" data-category="all">全部游戏</button>
                    <button class="filter-btn" data-category="战略">战略策略</button>
                    <button class="filter-btn" data-category="角色扮演">角色扮演</button>
                    <button class="filter-btn" data-category="休闲">休闲益智</button>
                    <button class="filter-btn" data-category="动作">动作冒险</button>
                    <button class="filter-btn" data-category="竞技">竞技体育</button>
                </div>
            </section> -->

            <!-- 游戏列表 -->
            <section class="games-grid" id="gamesContainer">
                <!-- 游戏卡片将通过JavaScript动态生成 -->
            </section>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <div class="contact-info">
                        <i class="fas fa-phone"></i>
                        <span>020-38383518</span>
                    </div>
                    <!-- <div class="contact-info">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div> -->
                    <div class="contact-info">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>广州市天河区黄埔大道中660号之-2301、2302房</span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-location"></i>
                        <span>广州立早网络科技有限公司</span>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="products-app.html">游戏中心</a></li>
                        <li><a href="articles.html">防沉迷公告</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>了解我们</h4>
                    <ul>
                        <li><a href="about.html">关于我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>抵制不良游戏，拒绝盗版游戏。注意自我保护，谨防受骗上当。适度游戏益脑，沉迷游戏伤身。合理安排时间，享受健康生活。</p>
                <p><a href="https://beian.miit.gov.cn" style="color:white">粤ICP备17009698号</a></p>
            </div>
        </div>
    </footer>

    <!-- 脚本 -->
    <script src="js/login.js"></script>
    <script>
        // 游戏数据
        const gamesList = [
            {
                id: 'g1',
                name: '诸侯征战',
                image: 'img/app/1/icon.png',
                images: [
                    'img/app/1/d1.jpg',
                    'img/app/1/d2.jpg',
                    'img/app/1/d3.jpg',
                    'img/app/1/d4.jpg',
                ],
                category: '战略',
                ageRating: '全年龄段',
                description: '一款复古纯粹的模拟经营三国手游。多人合作，疯狂国战，只有最好的策略才能赢得国战。大王快点来吹起集结号，奏响三国战歌！与兄弟们打下第一战国吧！ 在这里，您可以与朋友团结合作，统一指挥，千人国战；在这里，您需要警惕虎视眈眈的邻居，保卫自己的资源；在这里，您可以低调经营自己的城池，享受成长的满足；在这里，也可以纵横捭阖，领略与人争斗的其乐无穷。',
                features: [],
                publisher: '广州市勤众网络科技有限公司',
                supplier: '广州市勤众网络科技有限公司',
                contactPhone: '18715777095',
                downloadUrl: 'https://cdn.910app.com/android/downloads/zhzz-7750001-20250107164317.apk'
            },
            {
                id: 'g2',
                name: '朕的江山2',
                image: 'img/app/2/icon.png',
                images: [
                    'img/app/2/d1.jpg',
                    'img/app/2/d2.jpg',
                    'img/app/2/d3.jpg',
                    'img/app/2/d4.jpg',
                ],
                category: '战略',
                ageRating: '全年龄段',
                description: '《朕的江山2》是一款经典三国战役SLG手游。连城式大地图还原真实的三国战场，全服玩家同时征伐战场冲锋陷阵，四大兵种三类将领互相制衡，任君招募的上白名历史名将，高能的策略排兵布阵方能赢得国战，一统江山，战鼓齐鸣一起书写新的三国激战历史！',
                features: [],
                publisher: '广州立早网络科技有限公司',
                supplier: '广州立早网络科技有限公司',
                contactPhone: '020-38353818',
                downloadUrl: 'https://cdn.910app.com/android/downloads/zdjs2-60040040-20250325151214.apk'
            },
            {
                id: 'g3',
                name: '不朽大陆 ',
                image: 'img/app/3/icon.png',
                images: [
                    'img/app/3/d1.jpg',
                    'img/app/3/d2.jpg',
                    'img/app/3/d3.jpg',
                    'img/app/3/d4.jpg',
                ],
                category: '卡牌',
                ageRating: '全年龄段',
                description: '不朽大陆官方版是一款美漫式放置卡牌游戏，在故事背景中一场突如其来的未知病毒，爆发了外星人入侵的事实，为了阻止这场浩劫，你被选中为指挥官，现在你需要带领其他组织成员，寻找病毒源头并解放城市。',
                features: [],
                publisher: '武汉手盟网络科技股份有限公司',
                supplier: '武汉手盟网络科技股份有限公司',
                contactPhone: '027-85314686',
                downloadUrl: 'https://cdn.910app.com/android/downloads/bxdl-63560001-20250427120034.apk'
            },
            {
                id: 'g4',
                name: '攻城掠地',
                image: 'img/app/4/icon.png',
                images: [
                    'img/app/4/d1.jpg',
                    'img/app/4/d2.jpg',
                    'img/app/4/d3.jpg',
                    'img/app/4/d4.jpg',
                ],
                category: '策略',
                ageRating: '全年龄段',
                description: '《攻城掠地》是一款强调“国战”的战争策略游戏，本作由《傲视天地》原班人马创新打造，秉承经典，推陈出新。实景还原三国战图，包含多达300个关隘城池，开创全景即时国战!文臣武将齐数登场，打破“重武轻文”的游戏桎梏，内政事务、军师上阵、计策绝技演绎文官真本色!其次，递进掩杀式即时战斗模式、地形系统的引入，以及四大资源、战术、兵器、科技、皇城官职战等丰富的特色玩法，让你充分自由的享受鼎足争雄的热血，圆满开疆扩土、统一天下的宏梦!',
                features: [],
                publisher: '上海手盟网络科技有限公司',
                supplier: '上海手盟网络科技有限公司',
                contactPhone: '020-38204141',
                downloadUrl: 'https://cdn.910app.com/android/downloads/gcld.apk'
            },
            {
                id: 'g5',
                name: '割据天下',
                image: 'img/app/5/icon.png',
                images: [
                    'img/app/5/d1.jpg',
                    'img/app/5/d2.jpg',
                    'img/app/5/d3.jpg',
                    'img/app/5/d4.jpg',
                ],
                category: '战略',
                ageRating: '全年龄段',
                description: ' 《割据天下》是一款文明题材策略战争手游。游戏内设计了宏伟的战争场面，多元文化和不同种族的热血碰撞，你将亲自上阵，巧用天时地利人和，创立基业，组建联盟、自由扩张，智取天下，谱写属于你的传奇时代。',
                features: [],
                publisher: '武汉狙击手网络科技有限公司',
                supplier: '武汉狙击手网络科技有限公司',
                contactPhone: '18995622586',
                downloadUrl: 'https://cdn.910app.com/android/downloads/gjtx-67240001-20240510104416.apk'
            },
            {
                id: 'g6',
                name: '帝国霸权',
                image: 'img/app/6/icon.png',
                images: [
                    'img/app/6/d1.jpg',
                    'img/app/6/d2.jpg',
                    'img/app/6/d3.jpg',
                    'img/app/6/d4.jpg',
                ],
                category: '战略',
                ageRating: '16+',
                description: '帝国霸权是一款以中世纪时期罗马帝国为原型打造slg手游，玩家可以培养招募兵马训练将领，玩家将在游戏找那个建造自己的专属帝国，领袖意识，拥有多种不同种族的士兵，统帅一支强大的军队',
                features: [],
                publisher: '上海手盟网络科技有限公司',
                supplier: '上海手盟网络科技有限公司',
                contactPhone: '020-38204141',
                downloadUrl: 'https://cdn.910app.com/android/downloads/dgbq-24310040-20220629114329.apk'
            },
        ];

        // 动态生成游戏卡片
        function renderGames(games = gamesList) {
            const gamesContainer = document.getElementById('gamesContainer');
            gamesContainer.innerHTML = '';
            
            games.forEach(game => {
                const gameCard = document.createElement('div');
                gameCard.className = 'game-card';
                gameCard.innerHTML = `
                    <div class="game-image">
                        <img src="${game.image}" alt="${game.name}">
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">${game.name}</h3>
                        <div class="game-meta">
                            <span class="game-category">${game.category}</span>
                            <span class="game-age">${game.ageRating}</span>
                        </div>
                        <p class="game-description">${game.description}</p>
                        <div class="game-actions">
                            <a href="${game.downloadUrl}" class="download-btn">
                                <i class="fas fa-download"></i>
                                游戏下载
                            </a>
                            <a href="product-detail.html?id=${game.id}" class="detail-btn">
                                查看详情
                            </a>
                        </div>
                    </div>
                `;
                gamesContainer.appendChild(gameCard);
            });
        }

        // 分类筛选功能
        function initCategoryFilter() {
            const filterButtons = document.querySelectorAll('.filter-btn');
            
            filterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // 移除所有按钮的激活状态
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    // 激活当前按钮
                    button.classList.add('active');
                    
                    const category = button.dataset.category;
                    
                    if (category === 'all') {
                        renderGames(gamesList);
                    } else {
                        const filteredGames = gamesList.filter(game => game.category === category);
                        renderGames(filteredGames);
                    }
                });
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            renderGames();
            initCategoryFilter();
        });
    </script>
</body>
</html> 